package com.ruoyi.system.utils;

import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 气象信息记录页Word文档处理工具类
 */
@Slf4j
public class WeatherRecordWordUtils {

    /**
     * 生成气象信息记录页Word文档
     *
     * @param templatePath  模板文件路径
     * @param data          气象信息数据
     * @param sealImagePath 公章图片路径
     * @return Word文档字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateWeatherRecordDocument(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        log.info("开始生成气象信息记录页Word文档，模板路径: {}", templatePath);

        try (InputStream templateStream = getTemplateInputStream(templatePath)) {
            XWPFDocument document = new XWPFDocument(templateStream);

            // 1. 构建占位符参数
            Map<String, String> params = buildPlaceholderParams(data);

            // 2. 替换文档中的占位符
            replaceInDocumentBody(document, params);
            processParagraphsAndTables(document, params);

            // 3. 填充表格数据（插入时自动设置合并单元格）
            fillTableData(document, data);


            // 4. 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            log.info("气象信息记录页Word文档生成完成");
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("生成气象信息记录页Word文档失败", e);
            throw new Exception("生成Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建占位符参数
     */
    private static Map<String, String> buildPlaceholderParams(WordMeteorologicalVo data) {
        Map<String, String> params = new HashMap<>();

        // 基本信息占位符
        params.put("aircraftType", data.getAircraftType() != null ? data.getAircraftType() : "");
        params.put("registrationNumber", data.getRegistrationNumber() != null ? data.getRegistrationNumber() : "");
        params.put("flightDate", data.getFlightDate() != null ? data.getFlightDate() : "");

        // 合计信息占位符
        params.put("fuel", data.getFuel() != null ? data.getFuel() : "");
        params.put("groundTimeMinTotal", data.getGroundTimeMinTotal() != null ? data.getGroundTimeMinTotal() : "");
        params.put("airTimeMinTotal", data.getAirTimeMinTotal() != null ? data.getAirTimeMinTotal() : "");
        params.put("totalTimeMinTotal", data.getTotalTimeMinTotal() != null ? data.getTotalTimeMinTotal() : "");
        params.put("sortieCountTotal", data.getSortieCountTotal() != null ? data.getSortieCountTotal() : "");
        log.debug("构建占位符参数完成，参数数量: {}", params.size());
        return params;
    }

    /**
     * 替换正文段落中的占位符
     */
    private static void replaceInDocumentBody(XWPFDocument document, Map<String, String> params) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            replaceInParagraph(paragraph, params, true);
        }
    }

    /**
     * 替换段落和表格里的占位符
     */
    private static void processParagraphsAndTables(XWPFDocument document, Map<String, String> params) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, params, false);
        }
        for (XWPFTable table : document.getTables()) {
            processTable(table, params);
        }
    }

    /**
     * 处理表格中的占位符
     */
    private static void processTable(XWPFTable table, Map<String, String> params) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    replaceInParagraph(paragraph, params, false);
                }
                // 嵌套表格递归处理
                for (XWPFTable nestedTable : cell.getTables()) {
                    processTable(nestedTable, params);
                }
            }
        }
    }

    /**
     * 替换段落中的占位符
     */
    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> params, boolean bold) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null || text.isEmpty()) continue;

            boolean replaced = false;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                if (text.contains(placeholder)) {
                    text = text.replace(placeholder, entry.getValue());
                    replaced = true;
                }
            }

            if (replaced) {
                run.setText(text, 0);
                // 只对替换后的内容设置样式
                run.setFontFamily("宋体");
                run.setFontSize(9);
                run.setBold(bold);
                run.setColor("000000");
            }
        }
    }

    /**
     * 填充表格数据 - 根据表头定位填充
     */
    private static void fillTableData(XWPFDocument document, WordMeteorologicalVo data) {
        log.info("开始填充表格数据 - 根据表头定位");

        List<XWPFTable> tables = document.getTables();
        if (tables.isEmpty()) {
            log.warn("文档中没有找到表格");
            return;
        }

        // 处理第一个表格（主表格）
        XWPFTable mainTable = tables.get(0);

        // 分析表格结构，找到各个表头的位置
        TableHeaderInfo headerInfo = analyzeTableHeaders(mainTable);

        // 根据表头位置分别填充数据
        fillDataByHeaders(mainTable, headerInfo, data);

        log.info("表格数据填充完成");
    }

    /**
     * 表头信息结构
     */
    private static class TableHeaderInfo {
        int departureWeatherHeaderRow = -1;  // 始发地气象信息表头行
        int arrivalWeatherHeaderRow = -1;    // 目的地气象信息表头行
        int dynamicInfoHeaderRow = -1;       // 动态信息表头行
        int totalRow = -1;                   // 合计行
    }

    /**
     * 分析表格表头结构
     */
    private static TableHeaderInfo analyzeTableHeaders(XWPFTable table) {
        TableHeaderInfo headerInfo = new TableHeaderInfo();
        List<XWPFTableRow> rows = table.getRows();

        log.info("=== 开始分析表格表头结构 ===");
        log.info("表格总行数: {}", rows.size());

        for (int i = 0; i < rows.size(); i++) {
            String rowText = getRowText(rows.get(i));
            log.info("第{}行内容: [{}]", i, rowText);

            // 查找始发地气象信息表头
            if (headerInfo.departureWeatherHeaderRow == -1 &&
                    rowText.contains("始发地") && rowText.contains("天气") && !rowText.contains("目的地")) {
                headerInfo.departureWeatherHeaderRow = i;
                log.info("找到始发地气象信息表头: 第{}行", i);
            }

            // 查找目的地气象信息表头
            if (headerInfo.arrivalWeatherHeaderRow == -1 &&
                    rowText.contains("目的地") && rowText.contains("天气")) {
                headerInfo.arrivalWeatherHeaderRow = i;
                log.info("找到目的地气象信息表头: 第{}行", i);
            }

            // 查找动态信息表头
            if (headerInfo.dynamicInfoHeaderRow == -1) {
                // 多种识别方式
                boolean isDynamicHeader = false;

                // 方式1：包含"开车时刻"
                if (rowText.contains("开车时刻")) {
                    isDynamicHeader = true;
                    log.debug("通过'开车时刻'识别动态信息表头");
                }

                // 方式2：包含"始发地"和"目的地"和时间相关字段
                if (!isDynamicHeader && rowText.contains("始发地") && rowText.contains("目的地") &&
                        (rowText.contains("开车") || rowText.contains("起飞") || rowText.contains("时刻"))) {
                    isDynamicHeader = true;
                    log.debug("通过'始发地+目的地+时间'识别动态信息表头");
                }

                // 方式3：包含"动态信息"标题后的第一个表头
                if (!isDynamicHeader && rowText.contains("动态信息")) {
                    // 这是动态信息标题行，下一行可能是表头
                    log.debug("发现动态信息标题行: 第{}行", i);
                }

                // 方式4：包含多个时间字段
                if (!isDynamicHeader && rowText.contains("起飞") && rowText.contains("着陆")) {
                    isDynamicHeader = true;
                    log.debug("通过'起飞+着陆'识别动态信息表头");
                }

                if (isDynamicHeader) {
                    headerInfo.dynamicInfoHeaderRow = i;
                    log.info("找到动态信息表头: 第{}行, 内容: {}", i, rowText);
                }
            }

            // 查找合计行
            if (headerInfo.totalRow == -1 &&
                    (rowText.contains("剩余油量") || rowText.contains("合计"))) {
                headerInfo.totalRow = i;
                log.info("找到合计行: 第{}行", i);
            }
        }

        // 输出分析结果总结
        log.info("=== 表头分析结果总结 ===");
        log.info("始发地气象信息表头: 第{}行", headerInfo.departureWeatherHeaderRow);
        log.info("目的地气象信息表头: 第{}行", headerInfo.arrivalWeatherHeaderRow);
        log.info("动态信息表头: 第{}行", headerInfo.dynamicInfoHeaderRow);
        log.info("合计行: 第{}行", headerInfo.totalRow);
        log.info("=== 表头分析完成 ===");

        return headerInfo;
    }

    /**
     * 获取行的所有文本
     */
    private static String getRowText(XWPFTableRow row) {
        StringBuilder text = new StringBuilder();
        for (XWPFTableCell cell : row.getTableCells()) {
            text.append(cell.getText()).append(" ");
        }
        return text.toString();
    }

    /**
     * 根据表头位置分别填充数据
     */
    private static void fillDataByHeaders(XWPFTable table, TableHeaderInfo headerInfo, WordMeteorologicalVo data) {
        log.info("开始根据表头位置分别填充数据");

        // 1. 填充始发地气象信息数据
        if (headerInfo.departureWeatherHeaderRow != -1 &&
                data.getDepartureWeatherInfoList() != null && !data.getDepartureWeatherInfoList().isEmpty()) {

            int insertPosition = headerInfo.departureWeatherHeaderRow + 1;
            log.info("=== 填充始发地气象信息数据 ===");
            log.info("表头行: {}, 插入位置: {}, 数据条数: {}",
                    headerInfo.departureWeatherHeaderRow, insertPosition, data.getDepartureWeatherInfoList().size());

            for (int i = 0; i < data.getDepartureWeatherInfoList().size(); i++) {
                WordFlightWeatherInfoVo weather = data.getDepartureWeatherInfoList().get(i);
                log.info("处理始发地数据第{}条: 批次={}, 位置={}, 天气={}",
                        i + 1, weather.getBatch(), weather.getLocationName(), weather.getWeather());

                String[] rowData = buildWeatherRowData(weather);
                insertRowAtPosition(table, insertPosition, rowData, true); // 气象信息行需要合并
                insertPosition++; // 下一行插入位置
            }
        } else {
            log.warn("始发地气象信息数据为空或表头未找到");
        }

        // 2. 填充目的地气象信息数据
        if (headerInfo.arrivalWeatherHeaderRow != -1 &&
                data.getArrivalWeatherInfoList() != null && !data.getArrivalWeatherInfoList().isEmpty()) {

            // 计算已插入的始发地数据行数
            int departureInsertedRows = 0;
            if (headerInfo.departureWeatherHeaderRow != -1 && data.getDepartureWeatherInfoList() != null) {
                departureInsertedRows = data.getDepartureWeatherInfoList().size();
            }

            // 调整目的地插入位置，考虑已插入的始发地数据行
            int adjustedArrivalHeaderRow = headerInfo.arrivalWeatherHeaderRow + departureInsertedRows;
            int insertPosition = adjustedArrivalHeaderRow + 1;

            log.info("=== 填充目的地气象信息数据 ===");
            log.info("原始表头行: {}, 已插入始发地行数: {}, 调整后表头行: {}, 插入位置: {}, 数据条数: {}",
                    headerInfo.arrivalWeatherHeaderRow, departureInsertedRows, adjustedArrivalHeaderRow,
                    insertPosition, data.getArrivalWeatherInfoList().size());

            for (int i = 0; i < data.getArrivalWeatherInfoList().size(); i++) {
                WordFlightWeatherInfoVo weather = data.getArrivalWeatherInfoList().get(i);
                log.info("处理目的地数据第{}条: 批次={}, 位置={}, 天气={}",
                        i + 1, weather.getBatch(), weather.getLocationName(), weather.getWeather());

                String[] rowData = buildWeatherRowData(weather);
                insertRowAtPosition(table, insertPosition, rowData, true); // 气象信息行需要合并
                insertPosition++; // 下一行插入位置
            }
        } else {
            log.warn("目的地气象信息数据为空或表头未找到");
        }

        // 3. 填充动态信息数据
        if (headerInfo.dynamicInfoHeaderRow != -1 &&
                data.getDynamicInfoList() != null && !data.getDynamicInfoList().isEmpty()) {

            // 重新计算插入位置，考虑之前插入的数据行
            int baseInsertPosition = headerInfo.dynamicInfoHeaderRow + 1;

            // 计算已插入的行数
            int insertedRows = 0;
            if (headerInfo.departureWeatherHeaderRow != -1 && data.getDepartureWeatherInfoList() != null) {
                insertedRows += data.getDepartureWeatherInfoList().size();
            }
            if (headerInfo.arrivalWeatherHeaderRow != -1 && data.getArrivalWeatherInfoList() != null) {
                insertedRows += data.getArrivalWeatherInfoList().size();
            }

            int actualInsertPosition = baseInsertPosition + insertedRows;

            log.info("=== 填充动态信息数据 ===");
            log.info("动态信息表头行: {}", headerInfo.dynamicInfoHeaderRow);
            log.info("基础插入位置: {}", baseInsertPosition);
            log.info("已插入行数: {}", insertedRows);
            log.info("实际插入位置: {}", actualInsertPosition);
            log.info("动态信息数据条数: {}", data.getDynamicInfoList().size());

            for (int i = 0; i < data.getDynamicInfoList().size(); i++) {
                WordFlightWeatherDynamicVo dynamic = data.getDynamicInfoList().get(i);
                log.info("处理动态信息第{}条: 批次={}, 始发地={}, 目的地={}, 开车时刻={}",
                        i + 1, dynamic.getBatch(), dynamic.getDepartureLocation(),
                        dynamic.getArrivalLocation(), dynamic.getCarStartTime());

                String[] rowData = buildDynamicRowData(dynamic);
                insertRowAtPosition(table, actualInsertPosition, rowData, false); // 动态信息行不需要合并
                actualInsertPosition++; // 下一行插入位置
            }
        } else {
            if (headerInfo.dynamicInfoHeaderRow == -1) {
                log.error("❌ 动态信息表头未找到！");
            }
            if (data.getDynamicInfoList() == null || data.getDynamicInfoList().isEmpty()) {
                log.error("❌ 动态信息数据为空！");
            }
        }

        log.info("根据表头位置填充数据完成");
    }

    /**
     * 构建气象信息行数据（11列结构，为合并单元格做准备）
     */
    private static String[] buildWeatherRowData(WordFlightWeatherInfoVo weather) {
        // 11列结构，第8列填充能见度，第9列留空（将被合并），第10列填充QNH，第11列留空（将被合并）
        String[] rowData = new String[]{
                weather.getBatch() != null ? weather.getBatch() : "",                    // 第1列：批次
                weather.getLocationName() != null ? weather.getLocationName() : "",      // 第2列：始发地/目的地
                weather.getWeather() != null ? weather.getWeather() : "",                // 第3列：天气
                weather.getCloudHeight() != null ? weather.getCloudHeight() : "",        // 第4列：云高(m)
                weather.getTemperature() != null ? weather.getTemperature() : "",        // 第5列：温度(℃)
                weather.getWindDirection() != null ? weather.getWindDirection() : "",    // 第6列：风向(°)
                weather.getWindSpeed() != null ? weather.getWindSpeed() : "",            // 第7列：风速(m/s)
                weather.getVisibility() != null ? weather.getVisibility() : "",          // 第8列：能见度(m) - 合并单元格主列
                "",                                                                      // 第9列：空（将与第8列合并）
                weather.getQnh() != null ? weather.getQnh() : "",                        // 第10列：QNH(hPa) - 合并单元格主列
                ""                                                                       // 第11列：空（将与第10列合并）
        };

        log.info("构建气象信息行数据(11列-准备合并) - 批次:{}, 位置:{}, 天气:{}, 云高:{}, 温度:{}, 风向:{}, 风速:{}, 能见度:{}, 空列, QNH:{}, 空列",
                rowData[0], rowData[1], rowData[2], rowData[3], rowData[4], rowData[5], rowData[6], rowData[7], rowData[9]);
        return rowData;
    }

    /**
     * 构建动态信息行数据（保持11列结构，动态信息表格与气象信息表格结构不同）
     */
    private static String[] buildDynamicRowData(WordFlightWeatherDynamicVo dynamic) {
        // 动态信息表格保持11列结构
        String[] rowData = new String[]{
                dynamic.getBatch() != null ? dynamic.getBatch() : "",                           // 第1列：批次
                dynamic.getDepartureLocation() != null ? dynamic.getDepartureLocation() : "",  // 第2列：始发地
                dynamic.getArrivalLocation() != null ? dynamic.getArrivalLocation() : "",      // 第3列：目的地
                dynamic.getCarStartTime() != null ? dynamic.getCarStartTime() : "",            // 第4列：开车时刻
                dynamic.getTakeOffTime() != null ? dynamic.getTakeOffTime() : "",              // 第5列：起飞时刻
                dynamic.getLandingTime() != null ? dynamic.getLandingTime() : "",              // 第6列：着陆时刻
                dynamic.getCarStopTime() != null ? dynamic.getCarStopTime() : "",              // 第7列：关车时刻
                dynamic.getGroundTimeMin() != null ? dynamic.getGroundTimeMin() : "",          // 第8列：地面时间(分钟)
                dynamic.getAirTimeMin() != null ? dynamic.getAirTimeMin() : "",                // 第9列：空中时间(分钟)
                dynamic.getTotalTimeMin() != null ? dynamic.getTotalTimeMin() : "",            // 第10列：时间小计(分钟)
                dynamic.getSortieCount() != null ? dynamic.getSortieCount() : ""               // 第11列：架次
        };

        log.info("构建动态信息行数据(11列) - 批次:{}, 始发地:{}, 目的地:{}, 开车:{}, 起飞:{}, 着陆:{}, 关车:{}, 地面时间:{}, 空中时间:{}, 时间小计:{}, 架次:{}",
                rowData[0], rowData[1], rowData[2], rowData[3], rowData[4], rowData[5], rowData[6], rowData[7], rowData[8], rowData[9], rowData[10]);
        return rowData;
    }

    /**
     * 在指定位置插入行
     */
    private static void insertRowAtPosition(XWPFTable table, int position, String[] rowData, boolean needMerge) {
        log.info("在第{}行位置插入数据: [{}], 是否需要合并: {}", position, String.join(", ", rowData), needMerge);

        // 插入新行
        XWPFTableRow newRow = table.insertNewTableRow(position);

        // 获取表格的列数（参考表头行）
        List<XWPFTableRow> rows = table.getRows();
        int columnCount = 0;
        if (!rows.isEmpty()) {
            // 查找一个有效的表头行来确定列数
            for (XWPFTableRow row : rows) {
                int cellCount = row.getTableCells().size();
                if (cellCount > columnCount) {
                    columnCount = cellCount;
                }
            }
            log.debug("表格最大列数: {}", columnCount);
        }

        // 确保新行有足够的单元格
        while (newRow.getTableCells().size() < columnCount) {
            newRow.addNewTableCell();
        }
        log.debug("新行创建完成，实际列数: {}", newRow.getTableCells().size());

        // 填充数据
        List<XWPFTableCell> cells = newRow.getTableCells();
        log.info("开始填充数据 - 单元格数: {}, 数据长度: {}", cells.size(), rowData.length);

        for (int i = 0; i < cells.size(); i++) {
            XWPFTableCell cell = cells.get(i);
            String cellData = (i < rowData.length) ? rowData[i] : "";

            log.debug("填充第{}列: [{}]", i, cellData);

            try {
                // 清除现有内容
                while (cell.getParagraphs().size() > 0) {
                    cell.removeParagraph(0);
                }

                // 设置垂直居中
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

                // 创建新段落
                XWPFParagraph paragraph = cell.addParagraph();
                paragraph.setAlignment(ParagraphAlignment.CENTER);

                // 创建新 Run 并设置文本
                XWPFRun run = paragraph.createRun();
                run.setText(cellData);

                // 设置字体样式
                run.setFontFamily("宋体");
                run.setFontSize(9);
                run.setBold(false);
                run.setColor("000000");

                log.debug("第{}列填充成功: [{}]", i, cellData);

            } catch (Exception e) {
                log.error("填充第{}列失败: {}", i, e.getMessage());
            }
        }

        // 根据行类型决定是否设置合并单元格
        if (needMerge) {
            setMergedCellsForWeatherRow(newRow);
            log.info("已为气象信息行设置合并单元格");
        } else {
            log.info("动态信息行，跳过合并单元格设置");
        }

        log.info("数据插入完成，共填充{}列", cells.size());
    }

    /**
     * 为气象信息行设置合并单元格
     */
    private static void setMergedCellsForWeatherRow(XWPFTableRow row) {
        try {
            List<XWPFTableCell> cells = row.getTableCells();
            if (cells.size() >= 11) {
                // 气象信息行：合并第8-9列（能见度）和第10-11列（QNH）
                mergeTwoCellsInNewRow(cells.get(7), cells.get(8)); // 索引7和8对应第8-9列
                mergeTwoCellsInNewRow(cells.get(9), cells.get(10)); // 索引9和10对应第10-11列
                log.debug("已为气象信息行设置合并单元格：第8-9列（能见度）和第10-11列（QNH）");
            } else {
                log.warn("行列数不足，无法设置合并单元格，当前列数: {}", cells.size());
            }
        } catch (Exception e) {
            log.error("为气象信息行设置合并单元格失败", e);
        }
    }


    /**
     * 合并新行中的两个单元格
     */
    private static void mergeTwoCellsInNewRow(XWPFTableCell cell1, XWPFTableCell cell2) {
        try {
            // 设置合并属性
            if (cell1.getCTTc().getTcPr() == null) {
                cell1.getCTTc().addNewTcPr();
            }
            if (cell2.getCTTc().getTcPr() == null) {
                cell2.getCTTc().addNewTcPr();
            }

            // 设置水平合并
            cell1.getCTTc().getTcPr().addNewHMerge().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.RESTART);
            cell2.getCTTc().getTcPr().addNewHMerge().setVal(org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge.CONTINUE);

            log.debug("已合并两个单元格");
        } catch (Exception e) {
            log.error("合并新行单元格失败", e);
        }
    }

    /**
     * 获取模板文件输入流
     * 支持类路径资源和绝对路径
     */
    private static InputStream getTemplateInputStream(String templatePath) throws Exception {
        log.debug("尝试加载模板文件: {}", templatePath);

        // 首先尝试作为类路径资源加载
        InputStream resourceStream = WeatherRecordWordUtils.class.getClassLoader().getResourceAsStream(templatePath);
        if (resourceStream != null) {
            log.info("成功从类路径加载模板文件: {}", templatePath);
            return resourceStream;
        }

        // 如果类路径中没有找到，尝试作为文件系统路径加载
        try {
            InputStream fileStream = Files.newInputStream(Paths.get(templatePath));
            log.info("成功从文件系统加载模板文件: {}", templatePath);
            return fileStream;
        } catch (Exception e) {
            log.error("无法从文件系统加载模板文件: {}", templatePath, e);
        }

        // 都失败了，抛出异常
        throw new Exception("无法找到模板文件: " + templatePath +
                "。请检查文件是否存在于类路径或文件系统中。");
    }

    /**
     * 设置Word文档响应头
     */
    public static void setWordResponseHeaders(HttpServletResponse response, String fileName) throws IOException {
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
    }

    /**
     * 使用流方式将Word文档输出到前端
     */
    public static void streamWordDocumentToResponse(HttpServletResponse response, byte[] docBytes) throws IOException {
        log.debug("开始流式输出Word文档，文档大小: {} bytes", docBytes.length);

        try (OutputStream out = response.getOutputStream();
             BufferedOutputStream bufferedOut = new BufferedOutputStream(out)) {

            // 分块写入，提高大文件传输效率 8KB缓冲区
            int bufferSize = 8192;
            int offset = 0;

            while (offset < docBytes.length) {
                int length = Math.min(bufferSize, docBytes.length - offset);
                bufferedOut.write(docBytes, offset, length);
                offset += length;
            }

            bufferedOut.flush();
            log.debug("Word文档流式输出完成");

        } catch (IOException e) {
            log.error("流式输出Word文档失败", e);
            throw e;
        }
    }

    /**
     * 生成文件名
     */
    public static String generateFileName(String prefix, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + timestamp + "." + extension;
    }
}

