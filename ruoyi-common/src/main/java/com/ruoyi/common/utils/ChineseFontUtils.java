package com.ruoyi.common.utils;

import com.aspose.words.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 中文字体处理工具类
 * 专门用于解决Aspose Words 15.8.0版本的中文字体乱码问题
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class ChineseFontUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ChineseFontUtils.class);
    
    /**
     * 常用中文字体名称映射
     */
    private static final String[][] CHINESE_FONT_MAPPING = {
        {"宋体", "SimSun", "NSimSun", "MS Song"},
        {"新宋体", "NSimSun", "SimSun", "MS Song"},
        {"黑体", "SimHei", "Microsoft YaHei", "MS Hei"},
        {"微软雅黑", "Microsoft YaHei", "Sim<PERSON>ei", "MS YaHei"},
        {"楷体", "KaiTi", "Sim<PERSON><PERSON>", "MS Kai"},
        {"楷体_GB2312", "KaiTi_GB2312", "KaiTi", "<PERSON>m<PERSON>ai"},
        {"仿宋", "FangSong", "SimFang", "MS FangSong"},
        {"仿宋_GB2312", "FangSong_GB2312", "FangSong", "SimFang"},
        {"隶书", "LiSu", "MS LiSu"},
        {"幼圆", "YouYuan", "MS YouYuan"},
        {"华文细黑", "STXihei", "SimHei", "Microsoft YaHei"},
        {"华文黑体", "STHeiti", "SimHei", "Microsoft YaHei"},
        {"华文楷体", "STKaiti", "KaiTi", "SimKai"},
        {"华文宋体", "STSong", "SimSun", "NSimSun"},
        {"华文仿宋", "STFangsong", "FangSong", "SimFang"},
        {"方正舒体", "FZShuTi", "KaiTi", "SimKai"},
        {"方正姚体", "FZYaoti", "SimSun", "NSimSun"}
    };
    
    /**
     * 系统字体目录
     */
    private static final String[] FONT_DIRECTORIES = {
        "C:/Windows/Fonts/",           // Windows
        "C:/WINDOWS/Fonts/",           // Windows (大写)
        "/System/Library/Fonts/",      // macOS
        "/usr/share/fonts/",           // Linux
        "/usr/local/share/fonts/"      // Linux
    };
    
    /**
     * 配置文档的中文字体支持
     * 
     * @param doc Word文档对象
     */
    public static void configureChineseFontSupport(Document doc) {
        try {
            FontSettings fontSettings = new FontSettings();
            
            // 设置字体源
            configureFontSources(fontSettings);
            
            // 配置字体替换规则
            configureFontSubstitution(fontSettings);
            
            // 应用字体设置到文档
            doc.setFontSettings(fontSettings);
            
            logger.info("中文字体支持配置完成");
        } catch (Exception e) {
            logger.error("配置中文字体支持失败", e);
        }
    }
    
    /**
     * 配置字体源
     */
    private static void configureFontSources(FontSettings fontSettings) {
        try {
            ArrayList<FontSourceBase> fontSources = new ArrayList<>();
            
            // 添加系统字体源
            fontSources.add(new SystemFontSource());
            
            // 添加自定义字体目录
            for (String fontDir : FONT_DIRECTORIES) {
                File dir = new File(fontDir);
                if (dir.exists() && dir.isDirectory()) {
                    fontSources.add(new FolderFontSource(fontDir, true));
                    logger.debug("添加字体目录: {}", fontDir);
                }
            }
            
            // 设置字体源
            fontSettings.setFontsSources(fontSources.toArray(new FontSourceBase[0]));
            
        } catch (Exception e) {
            logger.warn("配置字体源失败", e);
        }
    }
    
    /**
     * 配置字体替换规则
     */
    private static void configureFontSubstitution(FontSettings fontSettings) {
        try {
            FontSubstitutionSettings substitutionSettings = fontSettings.getSubstitutionSettings();
            
            // 启用字体信息替换
            substitutionSettings.getFontInfoSubstitution().setEnabled(true);
            
            // 启用默认字体替换
            substitutionSettings.getDefaultFontSubstitution().setEnabled(true);
            substitutionSettings.getDefaultFontSubstitution().setDefaultFontName("SimSun");
            
            // 启用表格字体替换
            substitutionSettings.getTableSubstitution().setEnabled(true);
            
            // 添加中文字体替换规则
            for (String[] fontMapping : CHINESE_FONT_MAPPING) {
                String originalFont = fontMapping[0];
                String[] substitutes = Arrays.copyOfRange(fontMapping, 1, fontMapping.length);
                substitutionSettings.getTableSubstitution().addSubstitutes(originalFont, substitutes);
                logger.debug("添加字体替换规则: {} -> {}", originalFont, Arrays.toString(substitutes));
            }
            
        } catch (Exception e) {
            logger.warn("配置字体替换规则失败", e);
        }
    }
    
    /**
     * 获取推荐的PDF保存选项（针对中文优化）
     */
    public static PdfSaveOptions getChinesePdfSaveOptions() {
        PdfSaveOptions saveOptions = new PdfSaveOptions();
        
        try {
            // 嵌入所有字体，确保中文字体正确显示
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL);
            
            // 设置文本压缩
            saveOptions.setTextCompression(PdfTextCompression.FLATE);
            
            // 设置图像压缩
            saveOptions.setImageCompression(PdfImageCompression.JPEG);
            saveOptions.setJpegQuality(90);
            
            // 保留表单字段
            saveOptions.setPreserveFormFields(true);
            
            // 导出文档结构以支持Unicode
            saveOptions.setExportDocumentStructure(true);
            
            // 针对Aspose Words 15.8.0的特殊配置
            saveOptions.setUseAntiAliasing(true);
            saveOptions.setUseHighQualityRendering(true);
            
            // 设置PDF合规性（有助于字体嵌入）
            saveOptions.setCompliance(PdfCompliance.PDF_17);
            
            logger.debug("中文PDF保存选项配置完成");
        } catch (Exception e) {
            logger.warn("配置PDF保存选项失败，使用默认设置", e);
        }
        
        return saveOptions;
    }
    
    /**
     * 检查系统中可用的中文字体
     */
    public static List<String> getAvailableChineseFonts() {
        List<String> availableFonts = new ArrayList<>();
        
        try {
            FontSettings fontSettings = new FontSettings();
            configureFontSources(fontSettings);
            
            // 检查常用中文字体是否可用
            for (String[] fontMapping : CHINESE_FONT_MAPPING) {
                for (String fontName : fontMapping) {
                    // 这里可以添加字体检查逻辑
                    // 由于Aspose Words 15.8.0的API限制，暂时返回预定义列表
                    if (!availableFonts.contains(fontName)) {
                        availableFonts.add(fontName);
                    }
                }
            }
            
        } catch (Exception e) {
            logger.warn("检查可用字体失败", e);
        }
        
        return availableFonts;
    }
    
    /**
     * 为文档中的所有中文文本设置默认字体
     */
    public static void setDefaultChineseFont(Document doc, String fontName) {
        try {
            // 遍历文档中的所有段落
            NodeCollection paragraphs = doc.getChildNodes(NodeType.PARAGRAPH, true);
            for (int i = 0; i < paragraphs.getCount(); i++) {
                Paragraph paragraph = (Paragraph) paragraphs.get(i);
                NodeCollection runs = paragraph.getChildNodes(NodeType.RUN, true);
                
                for (int j = 0; j < runs.getCount(); j++) {
                    Run run = (Run) runs.get(j);
                    String text = run.getText();
                    
                    // 检查是否包含中文字符
                    if (containsChinese(text)) {
                        run.getFont().setName(fontName);
                        run.getFont().setNameFarEast(fontName);
                    }
                }
            }
            
            logger.info("为文档设置默认中文字体: {}", fontName);
        } catch (Exception e) {
            logger.warn("设置默认中文字体失败", e);
        }
    }
    
    /**
     * 检查文本是否包含中文字符
     */
    private static boolean containsChinese(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        for (char c : text.toCharArray()) {
            if (c >= 0x4E00 && c <= 0x9FFF) {
                return true;
            }
        }
        return false;
    }
}
