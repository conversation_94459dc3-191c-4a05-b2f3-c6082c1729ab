package com.ruoyi.common.utils;

import com.aspose.words.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Aspose Words 15.8.0 中文乱码修复工具
 * 专门针对该版本的API限制提供简化但有效的解决方案
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class AsposeWordsChineseFixUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(AsposeWordsChineseFixUtils.class);
    
    /**
     * 修复Word文档中的中文字体问题
     * 
     * @param doc Word文档对象
     */
    public static void fixChineseFontIssues(Document doc) {
        try {
            // 1. 设置文档默认字体
            setDocumentDefaultFonts(doc);
            
            // 2. 遍历并修复所有文本的字体
            fixAllTextFonts(doc);
            
            // 3. 修复表格中的字体
            fixTableFonts(doc);
            
            logger.info("中文字体问题修复完成");
        } catch (Exception e) {
            logger.error("修复中文字体问题失败", e);
        }
    }
    
    /**
     * 设置文档默认字体
     */
    private static void setDocumentDefaultFonts(Document doc) {
        try {
            // 设置Normal样式的字体
            Style normalStyle = doc.getStyles().get(StyleIdentifier.NORMAL);
            if (normalStyle != null) {
                normalStyle.getFont().setName("SimSun");
                normalStyle.getFont().setNameFarEast("SimSun");
                normalStyle.getFont().setNameAscii("SimSun");
                normalStyle.getFont().setNameOther("SimSun");
            }
            
            // 设置默认段落字体
            Style defaultParagraphFont = doc.getStyles().getDefaultParagraphFormat().getStyle();
            if (defaultParagraphFont != null) {
                defaultParagraphFont.getFont().setName("SimSun");
                defaultParagraphFont.getFont().setNameFarEast("SimSun");
            }
            
        } catch (Exception e) {
            logger.warn("设置文档默认字体失败", e);
        }
    }
    
    /**
     * 修复所有文本的字体
     */
    private static void fixAllTextFonts(Document doc) {
        try {
            // 遍历所有段落
            NodeCollection paragraphs = doc.getChildNodes(NodeType.PARAGRAPH, true);
            for (int i = 0; i < paragraphs.getCount(); i++) {
                Paragraph paragraph = (Paragraph) paragraphs.get(i);
                fixParagraphFonts(paragraph);
            }
            
        } catch (Exception e) {
            logger.warn("修复文本字体失败", e);
        }
    }
    
    /**
     * 修复段落中的字体
     */
    private static void fixParagraphFonts(Paragraph paragraph) {
        try {
            NodeCollection runs = paragraph.getChildNodes(NodeType.RUN, true);
            for (int i = 0; i < runs.getCount(); i++) {
                Run run = (Run) runs.get(i);
                String text = run.getText();
                
                // 如果包含中文字符，设置中文字体
                if (containsChinese(text)) {
                    run.getFont().setName("SimSun");
                    run.getFont().setNameFarEast("SimSun");
                    run.getFont().setNameAscii("SimSun");
                    run.getFont().setNameOther("SimSun");
                }
            }
            
        } catch (Exception e) {
            logger.warn("修复段落字体失败", e);
        }
    }
    
    /**
     * 修复表格中的字体
     */
    private static void fixTableFonts(Document doc) {
        try {
            NodeCollection tables = doc.getChildNodes(NodeType.TABLE, true);
            for (int i = 0; i < tables.getCount(); i++) {
                Table table = (Table) tables.get(i);
                
                // 遍历表格的所有行
                for (Row row : table.getRows()) {
                    // 遍历行中的所有单元格
                    for (Cell cell : row.getCells()) {
                        // 修复单元格中的段落字体
                        NodeCollection cellParagraphs = cell.getChildNodes(NodeType.PARAGRAPH, true);
                        for (int j = 0; j < cellParagraphs.getCount(); j++) {
                            Paragraph paragraph = (Paragraph) cellParagraphs.get(j);
                            fixParagraphFonts(paragraph);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            logger.warn("修复表格字体失败", e);
        }
    }
    
    /**
     * 检查文本是否包含中文字符
     */
    private static boolean containsChinese(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        
        for (char c : text.toCharArray()) {
            if (c >= 0x4E00 && c <= 0x9FFF) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取针对中文优化的PDF保存选项
     */
    public static PdfSaveOptions getOptimizedPdfSaveOptions() {
        PdfSaveOptions saveOptions = new PdfSaveOptions();
        
        try {
            // 嵌入所有字体
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL);
            
            // 设置文本压缩
            saveOptions.setTextCompression(PdfTextCompression.FLATE);
            
            // 设置图像压缩
            saveOptions.setImageCompression(PdfImageCompression.JPEG);
            saveOptions.setJpegQuality(90);
            
            // 保留表单字段
            saveOptions.setPreserveFormFields(true);
            
            // 导出文档结构
            saveOptions.setExportDocumentStructure(true);
            
            // 尝试设置高质量渲染（如果支持）
            try {
                saveOptions.setUseAntiAliasing(true);
                saveOptions.setUseHighQualityRendering(true);
            } catch (Exception e) {
                // 忽略不支持的选项
                logger.debug("某些PDF选项在当前版本中不支持");
            }
            
        } catch (Exception e) {
            logger.warn("配置PDF保存选项失败", e);
        }
        
        return saveOptions;
    }
    
    /**
     * Word转PDF的完整解决方案
     */
    public static boolean convertWordToPdfWithChineseFix(Document doc, java.io.OutputStream outputStream) {
        try {
            // 1. 修复中文字体问题
            fixChineseFontIssues(doc);
            
            // 2. 获取优化的保存选项
            PdfSaveOptions saveOptions = getOptimizedPdfSaveOptions();
            
            // 3. 保存为PDF
            doc.save(outputStream, saveOptions);
            
            logger.info("Word转PDF完成（已修复中文字体）");
            return true;
            
        } catch (Exception e) {
            logger.error("Word转PDF失败", e);
            return false;
        }
    }
    
    /**
     * 预处理Word文档，为PDF转换做准备
     */
    public static void preprocessDocumentForPdf(Document doc) {
        try {
            // 修复字体问题
            fixChineseFontIssues(doc);
            
            // 设置页面设置（如果需要）
            for (Section section : doc.getSections()) {
                PageSetup pageSetup = section.getPageSetup();
                // 确保页面设置正确
                pageSetup.setPaperSize(PaperSize.A4);
            }
            
            logger.info("文档预处理完成");
        } catch (Exception e) {
            logger.warn("文档预处理失败", e);
        }
    }
}
