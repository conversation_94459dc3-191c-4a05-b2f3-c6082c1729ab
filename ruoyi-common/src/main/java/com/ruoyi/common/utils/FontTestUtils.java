package com.ruoyi.common.utils;

import com.aspose.words.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 字体测试工具类
 * 用于测试和验证中文字体配置是否正确
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class FontTestUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(FontTestUtils.class);
    
    /**
     * 测试中文字体是否正确配置
     */
    public static boolean testChineseFontConfiguration() {
        try {
            // 创建测试文档
            Document doc = new Document();
            DocumentBuilder builder = new DocumentBuilder(doc);
            
            // 添加中文测试文本
            builder.writeln("中文字体测试 - Chinese Font Test");
            builder.writeln("宋体：这是宋体字体测试文本");
            builder.writeln("黑体：这是黑体字体测试文本");
            builder.writeln("微软雅黑：这是微软雅黑字体测试文本");
            builder.writeln("楷体：这是楷体字体测试文本");
            builder.writeln("仿宋：这是仿宋字体测试文本");
            
            // 配置中文字体支持
            ChineseFontUtils.configureChineseFontSupport(doc);
            
            // 尝试转换为PDF
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            PdfSaveOptions saveOptions = ChineseFontUtils.getChinesePdfSaveOptions();
            doc.save(pdfOutputStream, saveOptions);
            
            byte[] pdfBytes = pdfOutputStream.toByteArray();
            boolean success = pdfBytes.length > 0;
            
            if (success) {
                logger.info("中文字体配置测试成功，PDF大小: {} 字节", pdfBytes.length);
            } else {
                logger.error("中文字体配置测试失败，PDF生成失败");
            }
            
            return success;
            
        } catch (Exception e) {
            logger.error("中文字体配置测试失败", e);
            return false;
        }
    }
    
    /**
     * 检查系统字体目录
     */
    public static List<String> checkSystemFontDirectories() {
        List<String> existingDirs = new ArrayList<>();
        
        String[] fontDirs = {
            "C:/Windows/Fonts/",
            "C:/WINDOWS/Fonts/",
            "/System/Library/Fonts/",
            "/usr/share/fonts/",
            "/usr/local/share/fonts/"
        };
        
        for (String fontDir : fontDirs) {
            File dir = new File(fontDir);
            if (dir.exists() && dir.isDirectory()) {
                existingDirs.add(fontDir);
                logger.info("找到字体目录: {}", fontDir);
            }
        }
        
        return existingDirs;
    }
    
    /**
     * 列出指定目录下的中文字体文件
     */
    public static List<String> listChineseFontFiles(String fontDirectory) {
        List<String> chineseFonts = new ArrayList<>();
        
        try {
            File dir = new File(fontDirectory);
            if (!dir.exists() || !dir.isDirectory()) {
                return chineseFonts;
            }
            
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    String fileName = file.getName().toLowerCase();
                    if (fileName.endsWith(".ttf") || fileName.endsWith(".otf") || fileName.endsWith(".ttc")) {
                        // 检查是否是中文字体
                        if (isChineseFontFile(fileName)) {
                            chineseFonts.add(file.getAbsolutePath());
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            logger.warn("列出字体文件失败: {}", fontDirectory, e);
        }
        
        return chineseFonts;
    }
    
    /**
     * 判断是否是中文字体文件
     */
    private static boolean isChineseFontFile(String fileName) {
        String[] chineseFontKeywords = {
            "sim", "ms", "kai", "fang", "hei", "song", "yahei", "chinese", "chs", "cht"
        };
        
        for (String keyword : chineseFontKeywords) {
            if (fileName.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 生成字体配置报告
     */
    public static String generateFontConfigurationReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== 字体配置报告 ===\n");
        report.append("生成时间: ").append(new java.util.Date()).append("\n\n");
        
        // 检查系统字体目录
        report.append("1. 系统字体目录检查:\n");
        List<String> fontDirs = checkSystemFontDirectories();
        if (fontDirs.isEmpty()) {
            report.append("   未找到任何字体目录\n");
        } else {
            for (String dir : fontDirs) {
                report.append("   ✓ ").append(dir).append("\n");
            }
        }
        
        // 检查中文字体文件
        report.append("\n2. 中文字体文件检查:\n");
        int totalChineseFonts = 0;
        for (String dir : fontDirs) {
            List<String> chineseFonts = listChineseFontFiles(dir);
            totalChineseFonts += chineseFonts.size();
            report.append("   目录 ").append(dir).append(": ").append(chineseFonts.size()).append(" 个中文字体\n");
            for (String font : chineseFonts) {
                report.append("     - ").append(new File(font).getName()).append("\n");
            }
        }
        
        // 测试字体配置
        report.append("\n3. 字体配置测试:\n");
        boolean testResult = testChineseFontConfiguration();
        report.append("   配置测试结果: ").append(testResult ? "✓ 成功" : "✗ 失败").append("\n");
        
        // 总结
        report.append("\n4. 总结:\n");
        report.append("   - 找到字体目录: ").append(fontDirs.size()).append(" 个\n");
        report.append("   - 找到中文字体: ").append(totalChineseFonts).append(" 个\n");
        report.append("   - 配置状态: ").append(testResult ? "正常" : "异常").append("\n");
        
        if (!testResult) {
            report.append("\n建议:\n");
            report.append("   1. 确保系统已安装中文字体\n");
            report.append("   2. 检查Aspose Words许可证是否有效\n");
            report.append("   3. 验证字体文件权限是否正确\n");
        }
        
        return report.toString();
    }
    
    /**
     * 打印字体配置报告到日志
     */
    public static void logFontConfigurationReport() {
        String report = generateFontConfigurationReport();
        logger.info("字体配置报告:\n{}", report);
    }
}
