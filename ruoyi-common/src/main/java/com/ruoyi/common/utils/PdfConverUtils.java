package com.ruoyi.common.utils;

import com.aspose.words.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;

public class PdfConverUtils {
    private static final Logger logger = LoggerFactory.getLogger(PdfConverUtils.class);

    /**
     * @param inputStream  源文件输入流
     * @param outputStream pdf文件输出流
     **/
    public static boolean wordToPdfByAspose(InputStream inputStream, OutputStream outputStream) {

        // 验证License 若不验证则转化出的pdf文档会有水印产生
        if (!getLicense()) {
            return false;
        }
        try {
            // 将源文件保存在com.aspose.words.Document中，具体的转换格式依靠里面的save方法
            Document doc = new Document(inputStream);

            // 配置字体替换规则，解决中文乱码问题
            configureFontSubstitution(doc);

            // 配置PDF保存选项以支持中文字体
            PdfSaveOptions saveOptions = new PdfSaveOptions();

            // 设置字体嵌入模式，确保中文字体被正确嵌入
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL);

            // 设置文本压缩选项
            saveOptions.setTextCompression(PdfTextCompression.FLATE);

            // 设置图像压缩
            saveOptions.setImageCompression(PdfImageCompression.JPEG);
            saveOptions.setJpegQuality(90);

            // 保留表单字段
            saveOptions.setPreserveFormFields(true);

            // 导出文档结构以支持Unicode（针对15.8.0版本的优化）
            saveOptions.setExportDocumentStructure(true);

            // 使用配置的选项保存PDF
            doc.save(outputStream, saveOptions);

            logger.info("word转换pdf完毕，已配置中文字体支持");
        } catch (Exception e) {
            logger.error("Word转PDF失败", e);
            e.printStackTrace();
            return false;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                    logger.error("关闭输出流失败", e);
                    e.printStackTrace();
                }
            }
        }
        return true;
    }

    /**
     * 带中文字体优化的Word转PDF方法
     * @param inputStream  源文件输入流
     * @param outputStream pdf文件输出流
     * @return 转换是否成功
     */
    public static boolean wordToPdfWithChineseSupport(InputStream inputStream, OutputStream outputStream) {
        // 验证License
        if (!getLicense()) {
            return false;
        }

        try {
            Document doc = new Document(inputStream);

            // 配置字体替换规则，解决中文乱码问题
            configureFontSubstitution(doc);

            // 配置PDF保存选项以支持中文
            PdfSaveOptions saveOptions = new PdfSaveOptions();

            // 嵌入所有字体，确保中文字体正确显示
            saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL);

            // 设置文本压缩
            saveOptions.setTextCompression(PdfTextCompression.FLATE);

            // 设置图像压缩
            saveOptions.setImageCompression(PdfImageCompression.JPEG);
            saveOptions.setJpegQuality(90);

            // 保留表单字段
            saveOptions.setPreserveFormFields(true);

            // 导出文档结构以支持Unicode
            saveOptions.setExportDocumentStructure(true);

            // 针对Aspose Words 15.8.0的特殊配置
            saveOptions.setUseAntiAliasing(true);
            saveOptions.setUseHighQualityRendering(true);

            // 保存为PDF
            doc.save(outputStream, saveOptions);

            logger.info("Word转PDF完成，已优化中文字体支持");
            return true;

        } catch (Exception e) {
            logger.error("Word转PDF失败（中文优化版本）", e);
            return false;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                    logger.error("关闭输出流失败", e);
                }
            }
        }
    }

    /**
     * 配置字体替换规则，解决中文乱码问题
     * 针对Aspose Words 15.8.0版本的特殊优化
     */
    private static void configureFontSubstitution(Document doc) {
        try {
            FontSettings fontSettings = new FontSettings();

            // 设置字体源，包括系统字体和自定义字体
            ArrayList<FontSourceBase> fontSources = new ArrayList<>();

            // 添加系统字体源
            fontSources.add(new SystemFontSource());

            // 设置字体源
            fontSettings.setFontsSources(fontSources.toArray(new FontSourceBase[0]));

            // 配置字体替换规则
            FontSubstitutionSettings substitutionSettings = fontSettings.getSubstitutionSettings();

            // 启用字体信息替换
            substitutionSettings.getFontInfoSubstitution().setEnabled(true);

            // 启用默认字体替换
            substitutionSettings.getDefaultFontSubstitution().setEnabled(true);
            substitutionSettings.getDefaultFontSubstitution().setDefaultFontName("SimSun");

            // 启用表格字体替换
            substitutionSettings.getTableSubstitution().setEnabled(true);

            // 为常见的中文字体设置替换规则
            substitutionSettings.getTableSubstitution().addSubstitutes("宋体", new String[]{"SimSun", "NSimSun", "MS Song"});
            substitutionSettings.getTableSubstitution().addSubstitutes("黑体", new String[]{"SimHei", "Microsoft YaHei", "MS Hei"});
            substitutionSettings.getTableSubstitution().addSubstitutes("微软雅黑", new String[]{"Microsoft YaHei", "SimHei", "MS YaHei"});
            substitutionSettings.getTableSubstitution().addSubstitutes("楷体", new String[]{"KaiTi", "SimKai", "MS Kai"});
            substitutionSettings.getTableSubstitution().addSubstitutes("仿宋", new String[]{"FangSong", "SimFang", "MS FangSong"});

            // 应用字体设置到文档
            doc.setFontSettings(fontSettings);

            logger.info("字体替换规则配置完成，支持中文字体");
        } catch (Exception e) {
            logger.warn("配置字体替换规则失败，将使用默认设置", e);
        }
    }

    // 官方文档的要求 无需理会
    public static boolean getLicense() {
        boolean result = false;
        try {
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            ByteArrayInputStream is = new ByteArrayInputStream(s.getBytes());
            License asposeLic = new License();
            asposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

}
