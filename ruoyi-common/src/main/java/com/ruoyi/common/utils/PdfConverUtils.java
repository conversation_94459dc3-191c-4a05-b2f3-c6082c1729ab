package com.ruoyi.common.utils;

import com.aspose.words.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;

public class PdfConverUtils {
    private static final Logger logger = LoggerFactory.getLogger(PdfConverUtils.class);

    /**
     * @param inputStream  源文件输入流
     * @param outputStream pdf文件输出流
     **/
    public static boolean wordToPdfByAspose(InputStream inputStream, OutputStream outputStream) {

        // 验证License 若不验证则转化出的pdf文档会有水印产生
        if (!getLicense()) {
            return false;
        }
        try {
            // 将源文件保存在com.aspose.words.Document中，具体的转换格式依靠里面的save方法
            Document doc = new Document(inputStream);

            // 使用专门的中文修复工具
            AsposeWordsChineseFixUtils.fixChineseFontIssues(doc);

            // 获取优化的PDF保存选项
            PdfSaveOptions saveOptions = AsposeWordsChineseFixUtils.getOptimizedPdfSaveOptions();

            // 使用配置的选项保存PDF
            doc.save(outputStream, saveOptions);

            logger.info("word转换pdf完毕，已配置中文字体支持");
        } catch (Exception e) {
            logger.error("Word转PDF失败", e);
            e.printStackTrace();
            return false;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                    logger.error("关闭输出流失败", e);
                    e.printStackTrace();
                }
            }
        }
        return true;
    }

    /**
     * 带中文字体优化的Word转PDF方法
     * @param inputStream  源文件输入流
     * @param outputStream pdf文件输出流
     * @return 转换是否成功
     */
    public static boolean wordToPdfWithChineseSupport(InputStream inputStream, OutputStream outputStream) {
        // 验证License
        if (!getLicense()) {
            return false;
        }

        try {
            Document doc = new Document(inputStream);

            // 使用专门的中文修复工具
            AsposeWordsChineseFixUtils.fixChineseFontIssues(doc);

            // 获取优化的PDF保存选项
            PdfSaveOptions saveOptions = AsposeWordsChineseFixUtils.getOptimizedPdfSaveOptions();

            // 保存为PDF
            doc.save(outputStream, saveOptions);

            logger.info("Word转PDF完成，已优化中文字体支持");
            return true;

        } catch (Exception e) {
            logger.error("Word转PDF失败（中文优化版本）", e);
            return false;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                    logger.error("关闭输出流失败", e);
                }
            }
        }
    }



    // 官方文档的要求 无需理会
    public static boolean getLicense() {
        boolean result = false;
        try {
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            ByteArrayInputStream is = new ByteArrayInputStream(s.getBytes());
            License asposeLic = new License();
            asposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

}
