package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.AsposeWordsChineseFixUtils;
import com.ruoyi.common.utils.FontTestUtils;
import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 字体测试控制器
 * 用于测试中文字体修复效果
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Api("字体测试")
@RestController
@RequestMapping("/system/font-test")
public class FontTestController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(FontTestController.class);
    
    /**
     * 生成中文字体测试报告
     */
    @ApiOperation("生成字体配置报告")
    @GetMapping("/report")
    public AjaxResult generateFontReport() {
        try {
            String report = FontTestUtils.generateFontConfigurationReport();
            return success("字体配置报告生成成功", report);
        } catch (Exception e) {
            logger.error("生成字体配置报告失败", e);
            return error("生成报告失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试中文PDF导出
     */
    @ApiOperation("测试中文PDF导出")
    @GetMapping("/test-pdf")
    public void testChinesePdfExport(HttpServletResponse response) {
        try {
            // 创建测试文档
            Document doc = new Document();
            DocumentBuilder builder = new DocumentBuilder(doc);
            
            // 添加测试内容
            builder.writeln("中文字体测试文档");
            builder.writeln("===================");
            builder.writeln("");
            builder.writeln("1. 宋体测试：这是宋体字体的测试文本，包含各种中文字符。");
            builder.writeln("2. 黑体测试：这是黑体字体的测试文本，用于验证字体显示效果。");
            builder.writeln("3. 微软雅黑测试：这是微软雅黑字体的测试文本。");
            builder.writeln("4. 楷体测试：这是楷体字体的测试文本。");
            builder.writeln("5. 仿宋测试：这是仿宋字体的测试文本。");
            builder.writeln("");
            builder.writeln("特殊字符测试：");
            builder.writeln("中文标点：，。！？；：""''（）【】《》");
            builder.writeln("数字混合：2025年7月31日，版本15.8.0");
            builder.writeln("英文混合：Hello 世界 World 中国");
            
            // 添加表格测试
            builder.writeln("");
            builder.writeln("表格测试：");
            builder.startTable();
            builder.insertCell();
            builder.write("序号");
            builder.insertCell();
            builder.write("中文内容");
            builder.insertCell();
            builder.write("英文内容");
            builder.endRow();
            
            builder.insertCell();
            builder.write("1");
            builder.insertCell();
            builder.write("测试数据一");
            builder.insertCell();
            builder.write("Test Data 1");
            builder.endRow();
            
            builder.insertCell();
            builder.write("2");
            builder.insertCell();
            builder.write("测试数据二");
            builder.insertCell();
            builder.write("Test Data 2");
            builder.endRow();
            
            builder.endTable();
            
            // 使用修复工具转换为PDF
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            boolean success = AsposeWordsChineseFixUtils.convertWordToPdfWithChineseFix(doc, pdfOutputStream);
            
            if (!success) {
                throw new RuntimeException("PDF转换失败");
            }
            
            // 设置响应头
            String fileName = "中文字体测试_" + System.currentTimeMillis() + ".pdf";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
            
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            
            // 输出PDF
            response.getOutputStream().write(pdfOutputStream.toByteArray());
            response.getOutputStream().flush();
            
            logger.info("中文PDF测试文档生成成功");
            
        } catch (Exception e) {
            logger.error("生成中文PDF测试文档失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("生成测试文档失败: " + e.getMessage());
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        }
    }
    
    /**
     * 测试中文Word导出
     */
    @ApiOperation("测试中文Word导出")
    @GetMapping("/test-word")
    public void testChineseWordExport(HttpServletResponse response) {
        try {
            // 创建测试文档
            Document doc = new Document();
            DocumentBuilder builder = new DocumentBuilder(doc);
            
            // 添加测试内容（与PDF测试相同）
            builder.writeln("中文字体测试文档（Word版本）");
            builder.writeln("============================");
            builder.writeln("");
            builder.writeln("这是用于验证Word文档中文字体显示的测试文档。");
            builder.writeln("包含各种中文字符、标点符号和混合内容。");
            
            // 应用字体修复
            AsposeWordsChineseFixUtils.fixChineseFontIssues(doc);
            
            // 设置响应头
            String fileName = "中文字体测试_" + System.currentTimeMillis() + ".docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");
            
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            
            // 输出Word文档
            doc.save(response.getOutputStream(), com.aspose.words.SaveFormat.DOCX);
            
            logger.info("中文Word测试文档生成成功");
            
        } catch (Exception e) {
            logger.error("生成中文Word测试文档失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("生成测试文档失败: " + e.getMessage());
            } catch (Exception ex) {
                logger.error("写入错误响应失败", ex);
            }
        }
    }
    
    /**
     * 检查字体配置状态
     */
    @ApiOperation("检查字体配置状态")
    @GetMapping("/check-status")
    public AjaxResult checkFontStatus() {
        try {
            boolean testResult = FontTestUtils.testChineseFontConfiguration();
            
            if (testResult) {
                return success("字体配置正常", "中文字体支持已正确配置");
            } else {
                return error("字体配置异常", "中文字体支持配置可能存在问题");
            }
            
        } catch (Exception e) {
            logger.error("检查字体配置状态失败", e);
            return error("检查失败: " + e.getMessage());
        }
    }
}
